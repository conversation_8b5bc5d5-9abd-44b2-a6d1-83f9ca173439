import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  Logger,
  UploadedFile,
  UseInterceptors,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags, ApiConsumes } from '@nestjs/swagger';
import { ChatAiService } from './chatAi.service';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  UploadDocumentDto,
  UpdateDocumentDto,
  FetchSingleChatAiDto,
  FetchChatAisDto,
  FetchSingleDocumentDto,
  FetchDocumentContentDto,
  FetchDocumentsDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  RemoveDocumentDto,
  RefundCreditsDto,
  UpdateSystemPromptDto,
} from './dto/chatAi.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';

@ApiTags('ChatAI')
@Controller('users/app/chatai')
export class ChatAiController {
  private readonly logger = new Logger(ChatAiController.name);

  constructor(private readonly chatAiService: ChatAiService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-chatai')
  async setupChatAi(
    @Req() req: { user: User },
    @Body() createChatAiDto: CreateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.setupChatAi(userId, createChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-chatai')
  async updateChatAi(
    @Req() req: { user: User },
    @Body() updateChatAiDto: UpdateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAi(userId, updateChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-settings')
  async updateChatAiSetting(
    @Req() req: { user: User },
    @Body() updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAiSettings(
      userId,
      updateChatAiSettingDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-system-prompt')
  async updateSystemPrompt(
    @Req() req: { user: User },
    @Body() updateSystemPromptDto: UpdateSystemPromptDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateSystemPrompt(
      userId,
      updateSystemPromptDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-chatai')
  async getSingleChatAi(
    @Req() req: { user: User },
    @Query() query: FetchSingleChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleChatAi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-chatais')
  async getAllChatAis(
    @Req() req: { user: User },
    @Query() query: FetchChatAisDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getAllChatAis(userId, query);
  }

  // @UseGuards(JwtAuthGuard)
  // @ApiBearerAuth()
  // @Get('get-transactions')
  // async getTransactions(
  //   @Query() query: GetChatAiTransactionDto,
  //   @Req() req: { user: User },
  // ) {
  //   const userId = req.user.id;
  //   return await this.chatAiService.getAllTransactions(userId, query);
  // }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-chatai')
  async removeChatAi(
    @Req() req: { user: User },
    @Body() removeChatAiDto: RemoveChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeChatAi(userId, removeChatAiDto);
  }

  // ==================== Document Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('upload-document')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 20 * 1024 * 1024, // 20MB limit
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.uploadDocument(
      userId,
      file,
      uploadDocumentDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-documents')
  async getDocuments(
    @Query() query: FetchDocumentsDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocuments(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-document-content')
  async getDocumentContent(
    @Query() query: FetchDocumentContentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocumentContent(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-document')
  async getSingleDocument(
    @Query() query: FetchSingleDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleDocument(userId, query);
  }

  // ==================== Internal APIs (for ChatAI-SDK-Clean) ====================

  // Internal status update endpoint removed - ChatAI-SDK-Clean now updates database directly

  /**
   * Internal API for ChatAI-SDK-Clean to refund credits when document processing fails
   * This endpoint should only be called by ChatAI-SDK-Clean service
   */
  @Post('internal/refund-credits')
  async refundCredits(@Body() refundCreditsDto: RefundCreditsDto) {
    // Note: This is an internal API, so we don't use JWT auth
    // ChatAI-SDK-Clean should authenticate using the appId
    const result = await this.chatAiService.refundCredits(
      refundCreditsDto.appId,
      refundCreditsDto.userId,
      refundCreditsDto.actionType,
      refundCreditsDto.creditsToRefund || 1,
      refundCreditsDto.actionId,
      refundCreditsDto.reason,
    );

    if (!result.success) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: result.message,
      };
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      message: result.message,
      result: {
        creditsRefunded: result.creditsRefunded,
      },
    };
  }

  // NOTE: Test endpoint removed - ChatAI-SDK-Clean now handles credit deduction directly

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-document')
  async updateDocument(
    @Req() req: { user: User },
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateDocument(userId, updateDocumentDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-document')
  async removeDocument(
    @Req() req: { user: User },
    @Body() removeDocumentDto: RemoveDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeDocument(userId, removeDocumentDto);
  }

  // ==================== Chat Management ====================
  // Note: Chat functionality has been moved to ChatAI-SDK service
  // This service now only handles document management and app validation

  // ==================== Credit Management ====================
  // Note: Credit management has been moved to key-validator API optimization
}
