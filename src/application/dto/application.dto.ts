import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';
import { OriginType } from '../../origins/entities/origins.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';

export class CreateAppDto {
  @ApiProperty({
    description: 'Name of the application',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'App name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  appName: string;

  @ApiProperty({
    description: 'Description of the application',
    minLength: 10,
    maxLength: 100,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  appDescription: string;

  @ApiProperty({
    description: 'Chain ID of the application',
  })
  @IsNotEmpty()
  @IsNumber()
  chainId: number;
}

export class FetchSingleAppDto {
  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class FetchAppsDto extends PaginationDto {
  @ApiProperty({
    description: 'Chain Id',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value?.toString())
  chainId?: string;

  @ApiProperty({
    description: 'List style',
    required: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  list?: boolean;

  @ApiProperty({
    description: 'Type of the service',
    maxLength: 255,
    enum: [
      OriginType.BUNDLER,
      OriginType.PAYMASTER,
      OriginType.RELAYER,
      OriginType.CHATAI,
    ],
    required: false,
  })
  @IsOptional()
  @IsEnum({
    BUNDLER: 'bundler',
    PAYMASTER: 'paymaster',
    RELAYER: 'relayer',
    CHATAI: 'chatai',
  })
  type: string;
}

export class UpdateAppDto {
  @ApiPropertyOptional({
    description: 'ID of the application',
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Name of the application',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'App name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  appName: string;

  @ApiProperty({
    description: 'Description of the application',
    minLength: 10,
    maxLength: 100,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  appDescription: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if the application is active',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;
}

export class DeleteAppDto extends FetchSingleAppDto {}

export class WhitelistValidatorDto {
  @ApiProperty({
    description: 'Chain ID of the application',
  })
  @IsNotEmpty()
  @IsNumber()
  chainId: number;

  @ApiProperty({
    description: 'apikey of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.textWithoutSpace)
  apikey: string;

  @ApiProperty({
    description: 'Origin of frontend',
    required: true,
  })
  @IsNotEmpty()
  origin: string;

  @ApiProperty({
    description: 'Payload of paymaster',
    required: true,
  })
  @IsObject()
  @ApiProperty({
    type: 'object',
    properties: {
      method: { type: 'string' },
      params: { type: 'array' },
    },
  })
  payload: { method: string; params: [] };

  @ApiProperty({
    description: 'Type of the service',
    maxLength: 255,
    enum: [
      OriginType.BUNDLER,
      OriginType.PAYMASTER,
      OriginType.RELAYER,
      OriginType.CHATAI,
    ],
  })
  @IsNotEmpty()
  @IsEnum(OriginType)
  type: string;
}
