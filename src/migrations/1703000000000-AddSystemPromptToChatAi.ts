import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSystemPromptToChatAi1703000000000 implements MigrationInterface {
  name = 'AddSystemPromptToChatAi1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "chat_ai_projects" 
      ADD COLUMN "systemPrompt" text DEFAULT 'You are an AI assistant here to help users with their questions.'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "chat_ai_projects" 
      DROP COLUMN "systemPrompt"
    `);
  }
}
